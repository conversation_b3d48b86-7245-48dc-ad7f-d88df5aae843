package com.mycompany.inventorymanagement;

import javax.swing.*;
import javax.swing.table.*;
import java.awt.*;
import java.awt.event.*;
import java.sql.*;

public class CategoryManagementFrame extends JFrame {
    private JTable table;
    private DefaultTableModel tableModel;
    private JTextField nameField, descriptionField;

    public CategoryManagementFrame() {
        setTitle("Quản lý danh mục sản phẩm");
        setSize(700, 500);
        setDefaultCloseOperation(EXIT_ON_CLOSE);
        setLocationRelativeTo(null);

        JPanel mainPanel = new JPanel(new BorderLayout());
        
        // Panel nhập liệu
        JPanel inputPanel = new JPanel(new GridLayout(3, 2, 5, 5));
        inputPanel.setBorder(BorderFactory.createTitledBorder("Thông tin danh mục"));
        
        inputPanel.add(new JLabel("Tên danh mục:"));
        nameField = new JTextField();
        inputPanel.add(nameField);
        
        inputPanel.add(new JLabel("Mô tả:"));
        descriptionField = new JTextField();
        inputPanel.add(descriptionField);

        // Panel nút bấm
        JPanel buttonPanel = new JPanel(new FlowLayout());
        JButton addButton = new JButton("Thêm");
        addButton.addActionListener(e -> addCategory());
        JButton updateButton = new JButton("Sửa");
        updateButton.addActionListener(e -> updateCategory());
        JButton deleteButton = new JButton("Xóa");
        deleteButton.addActionListener(e -> deleteCategory());
        JButton backButton = new JButton("Quay lại");
        backButton.addActionListener(e -> {
            new MainFrame().setVisible(true);
            dispose();
        });

        buttonPanel.add(addButton);
        buttonPanel.add(updateButton);
        buttonPanel.add(deleteButton);
        buttonPanel.add(backButton);

        // Bảng hiển thị danh mục
        tableModel = new DefaultTableModel(new String[]{"ID", "Tên danh mục", "Mô tả", "Số sản phẩm", "Ngày tạo"}, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false; // Không cho phép edit trực tiếp
            }
        };
        table = new JTable(tableModel);
        table.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        table.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                int row = table.getSelectedRow();
                if (row != -1) {
                    nameField.setText(tableModel.getValueAt(row, 1).toString());
                    descriptionField.setText(tableModel.getValueAt(row, 2).toString());
                }
            }
        });

        JScrollPane scrollPane = new JScrollPane(table);
        scrollPane.setBorder(BorderFactory.createTitledBorder("Danh sách danh mục"));

        // Layout chính
        JPanel topPanel = new JPanel(new BorderLayout());
        topPanel.add(inputPanel, BorderLayout.NORTH);
        topPanel.add(buttonPanel, BorderLayout.SOUTH);

        mainPanel.add(topPanel, BorderLayout.NORTH);
        mainPanel.add(scrollPane, BorderLayout.CENTER);

        add(mainPanel);
        loadCategories();
    }

    private void loadCategories() {
        tableModel.setRowCount(0);
        try (Connection conn = DriverManager.getConnection("****************************************", "root", "")) {
            String sql = """
                SELECT c.id, c.name, c.description, 
                       COUNT(p.id) as product_count, 
                       DATE_FORMAT(c.created_at, '%d/%m/%Y %H:%i') as created_date
                FROM categories c 
                LEFT JOIN products p ON c.id = p.category_id 
                GROUP BY c.id, c.name, c.description, c.created_at
                ORDER BY c.name
                """;
            Statement stmt = conn.createStatement();
            ResultSet rs = stmt.executeQuery(sql);
            
            while (rs.next()) {
                tableModel.addRow(new Object[]{
                    rs.getInt("id"),
                    rs.getString("name"),
                    rs.getString("description"),
                    rs.getInt("product_count"),
                    rs.getString("created_date")
                });
            }
        } catch (SQLException ex) {
            JOptionPane.showMessageDialog(this, "Lỗi kết nối: " + ex.getMessage());
        }
    }

    private void addCategory() {
        String name = nameField.getText().trim();
        String description = descriptionField.getText().trim();

        if (name.isEmpty()) {
            JOptionPane.showMessageDialog(this, "Vui lòng nhập tên danh mục!");
            return;
        }

        try (Connection conn = DriverManager.getConnection("****************************************", "root", "")) {
            String sql = "INSERT INTO categories (name, description) VALUES (?, ?)";
            PreparedStatement stmt = conn.prepareStatement(sql);
            stmt.setString(1, name);
            stmt.setString(2, description);
            stmt.executeUpdate();
            
            JOptionPane.showMessageDialog(this, "Thêm danh mục thành công!");
            loadCategories();
            clearFields();
        } catch (SQLException ex) {
            if (ex.getErrorCode() == 1062) { // Duplicate entry
                JOptionPane.showMessageDialog(this, "Tên danh mục đã tồn tại!");
            } else {
                JOptionPane.showMessageDialog(this, "Lỗi kết nối: " + ex.getMessage());
            }
        }
    }

    private void updateCategory() {
        int row = table.getSelectedRow();
        if (row == -1) {
            JOptionPane.showMessageDialog(this, "Vui lòng chọn danh mục để sửa!");
            return;
        }

        String name = nameField.getText().trim();
        String description = descriptionField.getText().trim();

        if (name.isEmpty()) {
            JOptionPane.showMessageDialog(this, "Vui lòng nhập tên danh mục!");
            return;
        }

        int id = (int) tableModel.getValueAt(row, 0);

        try (Connection conn = DriverManager.getConnection("****************************************", "root", "")) {
            String sql = "UPDATE categories SET name = ?, description = ? WHERE id = ?";
            PreparedStatement stmt = conn.prepareStatement(sql);
            stmt.setString(1, name);
            stmt.setString(2, description);
            stmt.setInt(3, id);
            stmt.executeUpdate();
            
            JOptionPane.showMessageDialog(this, "Cập nhật danh mục thành công!");
            loadCategories();
            clearFields();
        } catch (SQLException ex) {
            if (ex.getErrorCode() == 1062) { // Duplicate entry
                JOptionPane.showMessageDialog(this, "Tên danh mục đã tồn tại!");
            } else {
                JOptionPane.showMessageDialog(this, "Lỗi kết nối: " + ex.getMessage());
            }
        }
    }

    private void deleteCategory() {
        int row = table.getSelectedRow();
        if (row == -1) {
            JOptionPane.showMessageDialog(this, "Vui lòng chọn danh mục để xóa!");
            return;
        }

        int productCount = (int) tableModel.getValueAt(row, 3);
        if (productCount > 0) {
            JOptionPane.showMessageDialog(this, 
                "Không thể xóa danh mục này vì còn " + productCount + " sản phẩm!");
            return;
        }

        int confirm = JOptionPane.showConfirmDialog(this, 
            "Bạn có chắc chắn muốn xóa danh mục này?", 
            "Xác nhận xóa", 
            JOptionPane.YES_NO_OPTION);

        if (confirm == JOptionPane.YES_OPTION) {
            int id = (int) tableModel.getValueAt(row, 0);

            try (Connection conn = DriverManager.getConnection("****************************************", "root", "")) {
                String sql = "DELETE FROM categories WHERE id = ?";
                PreparedStatement stmt = conn.prepareStatement(sql);
                stmt.setInt(1, id);
                stmt.executeUpdate();
                
                JOptionPane.showMessageDialog(this, "Xóa danh mục thành công!");
                loadCategories();
                clearFields();
            } catch (SQLException ex) {
                JOptionPane.showMessageDialog(this, "Lỗi kết nối: " + ex.getMessage());
            }
        }
    }

    private void clearFields() {
        nameField.setText("");
        descriptionField.setText("");
        table.clearSelection();
    }
}
