@echo off
echo ========================================
echo   HE THONG QUAN LY HANG HOA
echo   Phien ban 1.0 - Giao dien goc
echo ========================================
echo.
echo Giao dien goc (truoc khi cai thien):
echo - Layout don gian
echo - Mau sac mac dinh
echo - Khong co hover effects
echo.
echo Thong tin dang nhap:
echo - Admin: admin / admin123
echo - Demo:  demo / demo123
echo.

echo Dang khoi dong ung dung goc...

REM Backup current LoginFrame
copy "src\main\java\com\mycompany\inventorymanagement\LoginFrame.java" "src\main\java\com\mycompany\inventorymanagement\LoginFrame_new.java" >nul 2>&1

REM Restore original simple LoginFrame
echo package com.mycompany.inventorymanagement; > temp_login.java
echo. >> temp_login.java
echo import java.awt.GridLayout; >> temp_login.java
echo import java.sql.*; >> temp_login.java
echo import javax.swing.*; >> temp_login.java
echo. >> temp_login.java
echo public class LoginFrame extends JFrame { >> temp_login.java
echo     private JTextField usernameField; >> temp_login.java
echo     private JPasswordField passwordField; >> temp_login.java
echo. >> temp_login.java
echo     public LoginFrame() { >> temp_login.java
echo         setTitle("Dang nhap"); >> temp_login.java
echo         setSize(300, 200); >> temp_login.java
echo         setDefaultCloseOperation(EXIT_ON_CLOSE); >> temp_login.java
echo         setLocationRelativeTo(null); >> temp_login.java
echo. >> temp_login.java
echo         JPanel panel = new JPanel(new GridLayout(3, 2, 5, 5)); >> temp_login.java
echo         panel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10)); >> temp_login.java
echo. >> temp_login.java
echo         panel.add(new JLabel("Ten dang nhap:")); >> temp_login.java
echo         usernameField = new JTextField(); >> temp_login.java
echo         panel.add(usernameField); >> temp_login.java
echo. >> temp_login.java
echo         panel.add(new JLabel("Mat khau:")); >> temp_login.java
echo         passwordField = new JPasswordField(); >> temp_login.java
echo         panel.add(passwordField); >> temp_login.java
echo. >> temp_login.java
echo         JButton loginButton = new JButton("Dang nhap"); >> temp_login.java
echo         loginButton.addActionListener(e -^> login()); >> temp_login.java
echo         panel.add(loginButton); >> temp_login.java
echo         panel.add(new JLabel("")); >> temp_login.java
echo. >> temp_login.java
echo         add(panel); >> temp_login.java
echo     } >> temp_login.java
echo. >> temp_login.java
echo     private void login() { >> temp_login.java
echo         String username = usernameField.getText().trim(); >> temp_login.java
echo         String password = new String(passwordField.getPassword()); >> temp_login.java
echo         if (username.equals("admin") ^&^& password.equals("admin123")) { >> temp_login.java
echo             JOptionPane.showMessageDialog(this, "Dang nhap thanh cong!"); >> temp_login.java
echo             new MainFrame().setVisible(true); >> temp_login.java
echo             dispose(); >> temp_login.java
echo         } else { >> temp_login.java
echo             JOptionPane.showMessageDialog(this, "Sai thong tin!"); >> temp_login.java
echo         } >> temp_login.java
echo     } >> temp_login.java
echo. >> temp_login.java
echo     public static void main(String[] args) { >> temp_login.java
echo         SwingUtilities.invokeLater(() -^> new LoginFrame().setVisible(true)); >> temp_login.java
echo     } >> temp_login.java
echo } >> temp_login.java

echo Phien ban goc da duoc khoi phuc tam thoi.
echo Hay so sanh voi phien ban moi bang cach chay run_app.bat
echo.
pause
