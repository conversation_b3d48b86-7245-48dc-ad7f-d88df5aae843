# 📖 HƯỚNG DẪN SỬ DỤNG HỆ THỐNG

## 🎯 Tổng quan
Hệ thống Quản lý Hàng hóa cung cấp giao diện đơn giản để quản lý sản phẩm, danh mục và theo dõi tồn kho.

## 🔐 Đăng nhập hệ thống

### Bước 1: Khởi động ứng dụng
- Chạy file `LoginFrame.java` hoặc click vào shortcut
- Giao diện đăng nhập sẽ hiển thị

### Bước 2: Nhập thông tin
- **Tên đăng nhập**: `admin`
- **Mật khẩu**: `admin123`
- Click nút **"Đăng nhập"** hoặc nhấn Enter

### Bước 3: Truy cập thành công
- H<PERSON> thống sẽ chuyển đến Dashboard chính
- Hiển thị 6 chức năng chính

## 🏠 Dashboard - <PERSON><PERSON><PERSON> hình chính

### Giao diện Dashboard
Dashboard hiển thị 6 cards chức năng:

1. **🔍 Tra cứu sản phẩm** - T<PERSON><PERSON> kiếm thông tin sản phẩm
2. **📦 Quản lý sản phẩm** - Thêm/sửa/xóa sản phẩm  
3. **📂 Quản lý danh mục** - Quản lý danh mục sản phẩm
4. **📋 Nhập/Xuất kho** - Quản lý giao dịch kho
5. **📊 Báo cáo & Thống kê** - Xem báo cáo và thống kê
6. **🚪 Đăng xuất** - Thoát khỏi hệ thống

### Navigation
- Click vào bất kỳ card nào để truy cập chức năng
- Mỗi chức năng sẽ mở trong cửa sổ riêng
- Sử dụng nút "Quay lai" để về Dashboard

## 🔍 Tra cứu sản phẩm

### Tìm kiếm sản phẩm
1. **Nhập từ khóa**: Gõ tên sản phẩm vào ô "Tên hàng hóa"
2. **Click "Tìm kiếm"**: Hoặc nhấn Enter
3. **Xem kết quả**: Thông tin chi tiết hiển thị bên dưới

### Ví dụ tìm kiếm
- `chuot` → Tìm các loại chuột
- `ban` → Tìm các loại bàn
- `dien thoai` → Tìm điện thoại
- `may tinh` → Tìm máy tính

### Thông tin hiển thị
- **ID**: Mã sản phẩm
- **Tên**: Tên sản phẩm
- **Danh mục**: Loại sản phẩm
- **Số lượng**: Tồn kho hiện tại
- **Giá**: Giá bán (VND)
- **Tồn kho tối thiểu**: Mức cảnh báo
- **Mô tả**: Thông tin chi tiết

## 📦 Quản lý sản phẩm

### Xem danh sách sản phẩm
- Bảng hiển thị tất cả sản phẩm
- Có thể scroll để xem thêm
- Click vào dòng để chọn sản phẩm

### Thêm sản phẩm mới
1. **Điền thông tin**:
   - Tên sản phẩm (bắt buộc)
   - Chọn danh mục từ dropdown
   - Số lượng (số nguyên dương)
   - Giá (số thập phân)
   - Tồn kho tối thiểu
   - Mô tả (tùy chọn)

2. **Click "Thêm"**: Sản phẩm sẽ được thêm vào database

### Sửa sản phẩm
1. **Chọn sản phẩm**: Click vào dòng trong bảng
2. **Thông tin tự động điền**: Vào các ô input
3. **Chỉnh sửa**: Thay đổi thông tin cần thiết
4. **Click "Sửa"**: Lưu thay đổi

### Xóa sản phẩm
1. **Chọn sản phẩm**: Click vào dòng trong bảng
2. **Click "Xóa"**: Hệ thống sẽ hỏi xác nhận
3. **Xác nhận**: Click "Yes" để xóa

### Tìm kiếm trong danh sách
- Nhập từ khóa vào ô "Tìm kiếm"
- Click "Tìm kiếm" để lọc kết quả
- Kết quả hiển thị ngay trong bảng

## 📂 Quản lý danh mục

### Xem danh sách danh mục
Bảng hiển thị:
- **ID**: Mã danh mục
- **Tên danh mục**: Tên của danh mục
- **Mô tả**: Mô tả chi tiết
- **Số sản phẩm**: Số lượng sản phẩm trong danh mục
- **Ngày tạo**: Thời gian tạo danh mục

### Thêm danh mục mới
1. **Nhập thông tin**:
   - Tên danh mục (bắt buộc, không trùng)
   - Mô tả (tùy chọn)
2. **Click "Them"**: Danh mục mới được tạo

### Sửa danh mục
1. **Chọn danh mục**: Click vào dòng trong bảng
2. **Thông tin tự động điền**: Vào form
3. **Chỉnh sửa**: Thay đổi tên hoặc mô tả
4. **Click "Sua"**: Lưu thay đổi

### Xóa danh mục
1. **Chọn danh mục**: Click vào dòng trong bảng
2. **Click "Xoa"**: Hệ thống hỏi xác nhận
3. **Lưu ý**: Chỉ xóa được danh mục không có sản phẩm

### Làm mới dữ liệu
- Click "Lam moi" để reload danh sách
- Xóa thông tin trong form

## 📋 Nhập/Xuất kho

### Ghi nhận giao dịch nhập kho
1. **Chọn sản phẩm**: Từ dropdown hoặc nhập ID
2. **Nhập thông tin**:
   - Số lượng nhập (số dương)
   - Giá nhập
   - Lý do nhập (VD: "Nhập hàng đầu tháng")
   - Số chứng từ (VD: "NK001")
3. **Click "Nhập kho"**: Giao dịch được lưu

### Ghi nhận giao dịch xuất kho
1. **Chọn sản phẩm**: Từ dropdown hoặc nhập ID
2. **Nhập thông tin**:
   - Số lượng xuất (không vượt quá tồn kho)
   - Giá xuất
   - Lý do xuất (VD: "Bán cho khách hàng")
   - Số chứng từ (VD: "XK001")
3. **Click "Xuất kho"**: Giao dịch được lưu

### Xem lịch sử giao dịch
- Bảng hiển thị tất cả giao dịch
- Thông tin: Sản phẩm, loại giao dịch, số lượng, giá, lý do, ngày
- Có thể lọc theo sản phẩm hoặc loại giao dịch

## 📊 Báo cáo & Thống kê

### Báo cáo tồn kho
- Hiển thị tất cả sản phẩm với số lượng tồn
- Cảnh báo sản phẩm sắp hết (dưới mức tối thiểu)
- Tính tổng giá trị tồn kho

### Thống kê theo danh mục
- Số lượng sản phẩm mỗi danh mục
- Giá trị tồn kho theo danh mục
- Biểu đồ phân bố (nếu có)

### Báo cáo giao dịch
- Tổng số giao dịch nhập/xuất
- Giá trị nhập/xuất theo thời gian
- Top sản phẩm bán chạy

### Export báo cáo
- Click "Export" để xuất file Excel/PDF
- Chọn loại báo cáo và khoảng thời gian
- File sẽ được lưu vào thư mục Downloads

## 🚪 Đăng xuất

### Cách đăng xuất
1. **Từ Dashboard**: Click card "Đăng xuất"
2. **Từ menu**: File → Logout (nếu có)
3. **Xác nhận**: Click "Yes" trong dialog xác nhận

### Sau khi đăng xuất
- Hệ thống quay về màn hình đăng nhập
- Session được xóa
- Cần đăng nhập lại để sử dụng

## ⚠️ Lưu ý quan trọng

### Quy tắc nhập liệu
- **Tên sản phẩm**: Không được để trống, tối đa 255 ký tự
- **Số lượng**: Phải là số nguyên không âm
- **Giá**: Phải là số dương, có thể có phần thập phân
- **Tên danh mục**: Không được trùng lặp

### Xử lý lỗi
- **Lỗi kết nối**: Kiểm tra MySQL service
- **Lỗi nhập liệu**: Thông báo lỗi sẽ hiển thị
- **Lỗi quyền**: Đảm bảo đăng nhập với tài khoản đúng

### Backup dữ liệu
- Thường xuyên backup database
- Export dữ liệu quan trọng
- Kiểm tra tính toàn vẹn dữ liệu

### Performance tips
- Không mở quá nhiều cửa sổ cùng lúc
- Đóng các chức năng không sử dụng
- Restart ứng dụng nếu chạy chậm

## 🆘 Hỗ trợ

### Khi gặp sự cố
1. **Restart ứng dụng**: Đóng và mở lại
2. **Kiểm tra kết nối**: Đảm bảo MySQL đang chạy
3. **Xem log**: Kiểm tra thông báo lỗi
4. **Liên hệ admin**: Nếu vấn đề nghiêm trọng

### Shortcuts hữu ích
- **Enter**: Đăng nhập, tìm kiếm
- **Esc**: Đóng dialog
- **Tab**: Di chuyển giữa các trường
- **F5**: Refresh dữ liệu (nếu có)

### Best practices
- **Đăng xuất**: Luôn đăng xuất khi không sử dụng
- **Backup**: Thường xuyên backup dữ liệu
- **Update**: Cập nhật phần mềm khi có version mới
- **Security**: Không chia sẻ thông tin đăng nhập

---
*Hướng dẫn này bao gồm tất cả chức năng cơ bản. Để biết thêm chi tiết, vui lòng liên hệ bộ phận hỗ trợ.*
