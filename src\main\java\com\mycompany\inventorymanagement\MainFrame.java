package com.mycompany.inventorymanagement;

import java.awt.*;
import javax.swing.*;
import javax.swing.border.EmptyBorder;

public class MainFrame extends JFrame {
    public MainFrame() {
        initializeUI();
    }

    private void initializeUI() {
        setTitle("<PERSON><PERSON> thống <PERSON>ả<PERSON> lý <PERSON> hóa - Dashboard");
        setSize(900, 700);
        setDefaultCloseOperation(EXIT_ON_CLOSE);
        setLocationRelativeTo(null);
        setResizable(true);

        // Create main layout
        setLayout(new BorderLayout());

        // Header
        add(createHeaderPanel(), BorderLayout.NORTH);

        // Main content
        add(createMainContentPanel(), BorderLayout.CENTER);

        // Footer
        add(createFooterPanel(), BorderLayout.SOUTH);
    }

    private JPanel createHeaderPanel() {
        JPanel headerPanel = createGradientPanel(new Color(52, 152, 219), new Color(41, 128, 185));
        headerPanel.setLayout(new BorderLayout());
        headerPanel.setPreferredSize(new Dimension(0, 80));
        headerPanel.setBorder(new EmptyBorder(15, 30, 15, 30));

        // Title and welcome message
        JPanel titlePanel = new JPanel(new BorderLayout());
        titlePanel.setOpaque(false);

        JLabel titleLabel = new JLabel("📦 QUẢN LÝ HÀNG HÓA");
        titleLabel.setFont(new Font("Segoe UI", Font.BOLD, 24));
        titleLabel.setForeground(Color.WHITE);

        JLabel welcomeLabel = new JLabel("Chào mừng bạn đến với hệ thống quản lý");
        welcomeLabel.setFont(new Font("Segoe UI", Font.PLAIN, 14));
        welcomeLabel.setForeground(new Color(255, 255, 255, 180));

        titlePanel.add(titleLabel, BorderLayout.NORTH);
        titlePanel.add(welcomeLabel, BorderLayout.SOUTH);

        // Logout button
        JButton logoutButton = createStyledHeaderButton("Đăng xuất");
        logoutButton.addActionListener(e -> logout());

        headerPanel.add(titlePanel, BorderLayout.WEST);
        headerPanel.add(logoutButton, BorderLayout.EAST);

        return headerPanel;
    }

    private JPanel createGradientPanel(Color color1, Color color2) {
        return new JPanel() {
            @Override
            protected void paintComponent(Graphics g) {
                super.paintComponent(g);
                Graphics2D g2d = (Graphics2D) g;
                g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
                int w = getWidth(), h = getHeight();
                GradientPaint gp = new GradientPaint(0, 0, color1, 0, h, color2);
                g2d.setPaint(gp);
                g2d.fillRect(0, 0, w, h);
            }
        };
    }

    private JButton createStyledHeaderButton(String text) {
        JButton button = new JButton(text);
        button.setFont(new Font("Segoe UI", Font.BOLD, 14));
        button.setPreferredSize(new Dimension(150, 40));
        button.setBackground(new Color(255, 255, 255, 30));
        button.setForeground(Color.WHITE);
        button.setBorder(BorderFactory.createLineBorder(Color.WHITE, 1));
        button.setFocusPainted(false);
        button.setCursor(new Cursor(Cursor.HAND_CURSOR));

        // Hover effect
        button.addMouseListener(new java.awt.event.MouseAdapter() {
            @Override
            public void mouseEntered(java.awt.event.MouseEvent e) {
                button.setBackground(new Color(255, 255, 255, 50));
            }

            @Override
            public void mouseExited(java.awt.event.MouseEvent e) {
                button.setBackground(new Color(255, 255, 255, 30));
            }
        });

        return button;
    }

    private JPanel createMainContentPanel() {
        JPanel mainPanel = new JPanel(new BorderLayout());
        mainPanel.setBackground(new Color(236, 240, 241));
        mainPanel.setBorder(new EmptyBorder(30, 30, 30, 30));

        // Create dashboard cards
        JPanel cardsPanel = new JPanel(new GridLayout(2, 3, 20, 20));
        cardsPanel.setOpaque(false);

        // Add dashboard cards
        cardsPanel.add(createDashboardCard("🔍", "Tra cứu sản phẩm",
            "Tìm kiếm và xem thông tin sản phẩm", new Color(52, 152, 219), new Runnable() {
                @Override
                public void run() {
                    openSearchFrame();
                }
            }));

        cardsPanel.add(createDashboardCard("📦", "Quản lý sản phẩm",
            "Thêm, sửa, xóa sản phẩm", new Color(46, 204, 113), new Runnable() {
                @Override
                public void run() {
                    openCategoryFrame();
                }
            }));

        cardsPanel.add(createDashboardCard("📂", "Quản lý danh mục",
            "Quản lý danh mục sản phẩm", new Color(155, 89, 182), new Runnable() {
                @Override
                public void run() {
                    openCategoryManagementFrame();
                }
            }));

        cardsPanel.add(createDashboardCard("📋", "Nhập/Xuất kho",
            "Quản lý giao dịch kho hàng", new Color(243, 156, 18), new Runnable() {
                @Override
                public void run() {
                    openInventoryFrame();
                }
            }));

        cardsPanel.add(createDashboardCard("📊", "Báo cáo & Thống kê",
            "Xem báo cáo và thống kê", new Color(231, 76, 60), new Runnable() {
                @Override
                public void run() {
                    openBusinessFrame();
                }
            }));

        cardsPanel.add(createDashboardCard("⚙️", "Cài đặt",
            "Cấu hình hệ thống", new Color(52, 73, 94), new Runnable() {
                @Override
                public void run() {
                    showSettingsDialog();
                }
            }));

        mainPanel.add(cardsPanel, BorderLayout.CENTER);
        return mainPanel;
    }

    private JPanel createDashboardCard(String icon, String title, String description, Color color, Runnable action) {
        JPanel card = new JPanel();
        card.setLayout(new BorderLayout());
        card.setBackground(Color.WHITE);
        card.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(new Color(0, 0, 0, 20), 1),
            new EmptyBorder(20, 20, 20, 20)
        ));
        card.setCursor(new Cursor(Cursor.HAND_CURSOR));

        // Icon panel
        JPanel iconPanel = new JPanel(new FlowLayout(FlowLayout.CENTER));
        iconPanel.setOpaque(false);
        JLabel iconLabel = new JLabel(icon);
        iconLabel.setFont(new Font("Segoe UI Emoji", Font.PLAIN, 36));
        iconLabel.setForeground(color);
        iconPanel.add(iconLabel);

        // Content panel
        JPanel contentPanel = new JPanel(new BorderLayout());
        contentPanel.setOpaque(false);

        JLabel titleLabel = new JLabel(title, SwingConstants.CENTER);
        titleLabel.setFont(new Font("Segoe UI", Font.BOLD, 16));
        titleLabel.setForeground(new Color(44, 62, 80));

        JLabel descLabel = new JLabel("<html><center>" + description + "</center></html>", SwingConstants.CENTER);
        descLabel.setFont(new Font("Segoe UI", Font.PLAIN, 12));
        descLabel.setForeground(new Color(52, 73, 94));

        contentPanel.add(titleLabel, BorderLayout.NORTH);
        contentPanel.add(Box.createVerticalStrut(10), BorderLayout.CENTER);
        contentPanel.add(descLabel, BorderLayout.SOUTH);

        card.add(iconPanel, BorderLayout.NORTH);
        card.add(contentPanel, BorderLayout.CENTER);

        // Simple click handler without hover effects
        card.addMouseListener(new java.awt.event.MouseAdapter() {
            @Override
            public void mouseClicked(java.awt.event.MouseEvent e) {
                action.run();
            }
        });

        return card;
    }

    private JPanel createFooterPanel() {
        JPanel footerPanel = new JPanel(new FlowLayout(FlowLayout.CENTER));
        footerPanel.setBackground(new Color(52, 73, 94));
        footerPanel.setPreferredSize(new Dimension(0, 40));

        JLabel footerLabel = new JLabel("© 2024 Hệ thống Quản lý Hàng hóa - Phiên bản 2.0");
        footerLabel.setFont(new Font("Segoe UI", Font.PLAIN, 12));
        footerLabel.setForeground(Color.WHITE);

        footerPanel.add(footerLabel);
        return footerPanel;
    }

    // Action methods
    private void openSearchFrame() {
        new SearchFrame().setVisible(true);
        dispose();
    }

    private void openCategoryFrame() {
        new CategoryFrame().setVisible(true);
        dispose();
    }

    private void openCategoryManagementFrame() {
        new CategoryManagementFrame().setVisible(true);
        dispose();
    }

    private void openInventoryFrame() {
        new InventoryTransactionFrame().setVisible(true);
        dispose();
    }

    private void openBusinessFrame() {
        new BussinessFrame().setVisible(true);
        dispose();
    }

    private void showSettingsDialog() {
        JOptionPane.showMessageDialog(this, "Chức năng cài đặt sẽ được phát triển trong phiên bản tiếp theo!",
            "Thông báo", JOptionPane.INFORMATION_MESSAGE);
    }

    private void logout() {
        int confirm = JOptionPane.showConfirmDialog(this, "Bạn có chắc chắn muốn đăng xuất?",
            "Xác nhận", JOptionPane.YES_NO_OPTION);
        if (confirm == JOptionPane.YES_OPTION) {
            new LoginFrame().setVisible(true);
            dispose();
        }
    }
}
