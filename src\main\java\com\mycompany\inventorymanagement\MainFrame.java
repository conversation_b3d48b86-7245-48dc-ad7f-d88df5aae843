package com.mycompany.inventorymanagement;

import java.awt.Dimension;
import java.awt.GridLayout;

import javax.swing.BorderFactory;
import javax.swing.JButton;
import javax.swing.JFrame;
import javax.swing.JOptionPane;
import javax.swing.JPanel;

public class MainFrame extends JFrame {
    public MainFrame() {
        setTitle("Hệ thống Quản lý Hàng hóa");
        setSize(500, 400);
        setDefaultCloseOperation(EXIT_ON_CLOSE);
        setLocationRelativeTo(null);

        JPanel panel = new JPanel(new GridLayout(3, 2, 10, 10));
        panel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));

        // Hàng 1: Tra cứu và Quản lý sản phẩm
        JButton searchButton = new JButton("🔍 Tra cứu sản phẩm");
        searchButton.setPreferredSize(new Dimension(200, 60));
        searchButton.addActionListener(e -> {
            new SearchFrame().setVisible(true);
            dispose();
        });

        JButton categoryButton = new JButton("📦 Quản lý sản phẩm");
        categoryButton.setPreferredSize(new Dimension(200, 60));
        categoryButton.addActionListener(e -> {
            new CategoryFrame().setVisible(true);
            dispose();
        });

        // Hàng 2: Quản lý danh mục và Nhập/Xuất kho
        JButton categoryMgmtButton = new JButton("📂 Quản lý danh mục");
        categoryMgmtButton.setPreferredSize(new Dimension(200, 60));
        categoryMgmtButton.addActionListener(e -> {
            new CategoryManagementFrame().setVisible(true);
            dispose();
        });

        JButton inventoryButton = new JButton("📋 Nhập/Xuất kho");
        inventoryButton.setPreferredSize(new Dimension(200, 60));
        inventoryButton.addActionListener(e -> {
            new InventoryTransactionFrame().setVisible(true);
            dispose();
        });

        // Hàng 3: Báo cáo và Đăng xuất
        JButton businessButton = new JButton("📊 Báo cáo & Thống kê");
        businessButton.setPreferredSize(new Dimension(200, 60));
        businessButton.addActionListener(e -> {
            new BussinessFrame().setVisible(true);
            dispose();
        });

        JButton logoutButton = new JButton("🚪 Đăng xuất");
        logoutButton.setPreferredSize(new Dimension(200, 60));
        logoutButton.addActionListener(e -> {
            int confirm = JOptionPane.showConfirmDialog(this,
                "Bạn có chắc chắn muốn đăng xuất?",
                "Xác nhận đăng xuất",
                JOptionPane.YES_NO_OPTION);
            if (confirm == JOptionPane.YES_OPTION) {
                new LoginFrame().setVisible(true);
                dispose();
            }
        });

        panel.add(searchButton);
        panel.add(categoryButton);
        panel.add(categoryMgmtButton);
        panel.add(inventoryButton);
        panel.add(businessButton);
        panel.add(logoutButton);

        add(panel);
    }
}
