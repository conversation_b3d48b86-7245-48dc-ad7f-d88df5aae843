package com.mycompany.inventorymanagement;

import java.awt.*;
import javax.swing.*;
import javax.swing.border.EmptyBorder;

public class MainFrame extends JFrame {
    public MainFrame() {
        initializeUI();
    }

    private void initializeUI() {
        setTitle("<PERSON><PERSON> thống <PERSON>ả<PERSON> lý <PERSON> hóa - Dashboard");
        setSize(900, 700);
        setDefaultCloseOperation(EXIT_ON_CLOSE);
        setLocationRelativeTo(null);
        setResizable(true);

        // Create main layout
        setLayout(new BorderLayout());

        // Header
        add(createHeaderPanel(), BorderLayout.NORTH);

        // Main content
        add(createMainContentPanel(), BorderLayout.CENTER);

        // Footer
        add(createFooterPanel(), BorderLayout.SOUTH);
    }

    private JPanel createHeaderPanel() {
        JPanel headerPanel = UIUtils.createGradientPanel(UIUtils.PRIMARY_COLOR, UIUtils.PRIMARY_DARK);
        headerPanel.setLayout(new BorderLayout());
        headerPanel.setPreferredSize(new Dimension(0, 80));
        headerPanel.setBorder(new EmptyBorder(15, 30, 15, 30));

        // Title and welcome message
        JPanel titlePanel = new JPanel(new BorderLayout());
        titlePanel.setOpaque(false);

        JLabel titleLabel = new JLabel("📦 QUẢN LÝ HÀNG HÓA");
        titleLabel.setFont(new Font("Segoe UI", Font.BOLD, 24));
        titleLabel.setForeground(Color.WHITE);

        JLabel welcomeLabel = new JLabel("Chào mừng bạn đến với hệ thống quản lý");
        welcomeLabel.setFont(new Font("Segoe UI", Font.PLAIN, 14));
        welcomeLabel.setForeground(new Color(255, 255, 255, 180));

        titlePanel.add(titleLabel, BorderLayout.NORTH);
        titlePanel.add(welcomeLabel, BorderLayout.SOUTH);

        // Logout button
        JButton logoutButton = createStyledHeaderButton("🚪 Đăng xuất");
        logoutButton.addActionListener(e -> logout());

        headerPanel.add(titlePanel, BorderLayout.WEST);
        headerPanel.add(logoutButton, BorderLayout.EAST);

        return headerPanel;
    }

    private JButton createStyledHeaderButton(String text) {
        JButton button = new JButton(text);
        button.setFont(UIUtils.BUTTON_FONT);
        button.setPreferredSize(new Dimension(150, 40));
        button.setBackground(new Color(255, 255, 255, 30));
        button.setForeground(Color.WHITE);
        button.setBorder(BorderFactory.createLineBorder(Color.WHITE, 1));
        button.setFocusPainted(false);
        button.setCursor(new Cursor(Cursor.HAND_CURSOR));

        // Hover effect
        button.addMouseListener(new java.awt.event.MouseAdapter() {
            @Override
            public void mouseEntered(java.awt.event.MouseEvent e) {
                button.setBackground(new Color(255, 255, 255, 50));
            }

            @Override
            public void mouseExited(java.awt.event.MouseEvent e) {
                button.setBackground(new Color(255, 255, 255, 30));
            }
        });

        return button;
    }

    private JPanel createMainContentPanel() {
        JPanel mainPanel = new JPanel(new BorderLayout());
        mainPanel.setBackground(UIUtils.LIGHT_GRAY);
        mainPanel.setBorder(new EmptyBorder(30, 30, 30, 30));

        // Create dashboard cards
        JPanel cardsPanel = new JPanel(new GridLayout(2, 3, 20, 20));
        cardsPanel.setOpaque(false);

        // Add dashboard cards
        cardsPanel.add(createDashboardCard("🔍", "Tra cứu sản phẩm",
            "Tìm kiếm và xem thông tin sản phẩm", UIUtils.PRIMARY_COLOR, () -> openSearchFrame()));

        cardsPanel.add(createDashboardCard("📦", "Quản lý sản phẩm",
            "Thêm, sửa, xóa sản phẩm", UIUtils.SECONDARY_COLOR, () -> openCategoryFrame()));

        cardsPanel.add(createDashboardCard("📂", "Quản lý danh mục",
            "Quản lý danh mục sản phẩm", UIUtils.ACCENT_COLOR, () -> openCategoryManagementFrame()));

        cardsPanel.add(createDashboardCard("📋", "Nhập/Xuất kho",
            "Quản lý giao dịch kho hàng", UIUtils.WARNING_COLOR, () -> openInventoryFrame()));

        cardsPanel.add(createDashboardCard("📊", "Báo cáo & Thống kê",
            "Xem báo cáo và thống kê", UIUtils.DANGER_COLOR, () -> openBusinessFrame()));

        cardsPanel.add(createDashboardCard("⚙️", "Cài đặt",
            "Cấu hình hệ thống", UIUtils.DARK_GRAY, () -> showSettingsDialog()));

        mainPanel.add(cardsPanel, BorderLayout.CENTER);
        return mainPanel;
    }

    private JPanel createDashboardCard(String icon, String title, String description, Color color, Runnable action) {
        JPanel card = new JPanel();
        card.setLayout(new BorderLayout());
        card.setBackground(Color.WHITE);
        card.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(new Color(0, 0, 0, 20), 1),
            new EmptyBorder(20, 20, 20, 20)
        ));
        card.setCursor(new Cursor(Cursor.HAND_CURSOR));

        // Icon panel
        JPanel iconPanel = new JPanel(new FlowLayout(FlowLayout.CENTER));
        iconPanel.setOpaque(false);
        JLabel iconLabel = new JLabel(icon);
        iconLabel.setFont(new Font("Segoe UI Emoji", Font.PLAIN, 36));
        iconLabel.setForeground(color);
        iconPanel.add(iconLabel);

        // Content panel
        JPanel contentPanel = new JPanel(new BorderLayout());
        contentPanel.setOpaque(false);

        JLabel titleLabel = new JLabel(title, SwingConstants.CENTER);
        titleLabel.setFont(new Font("Segoe UI", Font.BOLD, 16));
        titleLabel.setForeground(UIUtils.BLACK);

        JLabel descLabel = new JLabel("<html><center>" + description + "</center></html>", SwingConstants.CENTER);
        descLabel.setFont(new Font("Segoe UI", Font.PLAIN, 12));
        descLabel.setForeground(UIUtils.DARK_GRAY);

        contentPanel.add(titleLabel, BorderLayout.NORTH);
        contentPanel.add(Box.createVerticalStrut(10), BorderLayout.CENTER);
        contentPanel.add(descLabel, BorderLayout.SOUTH);

        card.add(iconPanel, BorderLayout.NORTH);
        card.add(contentPanel, BorderLayout.CENTER);

        // Hover effect
        card.addMouseListener(new java.awt.event.MouseAdapter() {
            @Override
            public void mouseEntered(java.awt.event.MouseEvent e) {
                card.setBackground(new Color(color.getRed(), color.getGreen(), color.getBlue(), 20));
                card.setBorder(BorderFactory.createCompoundBorder(
                    BorderFactory.createLineBorder(color, 2),
                    new EmptyBorder(19, 19, 19, 19)
                ));
            }

            @Override
            public void mouseExited(java.awt.event.MouseEvent e) {
                card.setBackground(Color.WHITE);
                card.setBorder(BorderFactory.createCompoundBorder(
                    BorderFactory.createLineBorder(new Color(0, 0, 0, 20), 1),
                    new EmptyBorder(20, 20, 20, 20)
                ));
            }

            @Override
            public void mouseClicked(java.awt.event.MouseEvent e) {
                action.run();
            }
        });

        return card;
    }

    private JPanel createFooterPanel() {
        JPanel footerPanel = new JPanel(new FlowLayout(FlowLayout.CENTER));
        footerPanel.setBackground(UIUtils.DARK_GRAY);
        footerPanel.setPreferredSize(new Dimension(0, 40));

        JLabel footerLabel = new JLabel("© 2024 Hệ thống Quản lý Hàng hóa - Phiên bản 1.0");
        footerLabel.setFont(new Font("Segoe UI", Font.PLAIN, 12));
        footerLabel.setForeground(Color.WHITE);

        footerPanel.add(footerLabel);
        return footerPanel;
    }

    // Action methods
    private void openSearchFrame() {
        new SearchFrame().setVisible(true);
        dispose();
    }

    private void openCategoryFrame() {
        new CategoryFrame().setVisible(true);
        dispose();
    }

    private void openCategoryManagementFrame() {
        new CategoryManagementFrame().setVisible(true);
        dispose();
    }

    private void openInventoryFrame() {
        new InventoryTransactionFrame().setVisible(true);
        dispose();
    }

    private void openBusinessFrame() {
        new BussinessFrame().setVisible(true);
        dispose();
    }

    private void showSettingsDialog() {
        UIUtils.showWarningMessage(this, "Chức năng cài đặt sẽ được phát triển trong phiên bản tiếp theo!");
    }

    private void logout() {
        int confirm = UIUtils.showConfirmDialog(this, "Bạn có chắc chắn muốn đăng xuất?");
        if (confirm == JOptionPane.YES_OPTION) {
            new LoginFrame().setVisible(true);
            dispose();
        }
    }
}
