package com.mycompany.inventorymanagement;

import javax.swing.*;
import java.awt.*;
import java.sql.*;
import java.text.NumberFormat;
import java.util.Locale;

public class SearchFrame extends JFrame {
    private JTextField searchField;
    private JTextArea resultArea;
    private NumberFormat currencyFormat;

    public SearchFrame() {
        setTitle("Tra cứu hàng hóa");
        setSize(500, 400);
        setDefaultCloseOperation(EXIT_ON_CLOSE);
        setLocationRelativeTo(null);

        // Khởi tạo định dạng tiền tệ Việt Nam
        currencyFormat = NumberFormat.getCurrencyInstance(new Locale("vi", "VN"));

        JPanel panel = new JPanel(new BorderLayout());
        JPanel topPanel = new JPanel(new FlowLayout());
        searchField = new JTextField(20);
        JButton searchButton = new JButton("<PERSON><PERSON><PERSON> kiếm");
        searchButton.addActionListener(e -> search());
        JButton backButton = new JButton("Quay lại");
        backButton.addActionListener(e -> {
            new MainFrame().setVisible(true);
            dispose();
        });

        topPanel.add(new JLabel("Tên hàng hóa: "));
        topPanel.add(searchField);
        topPanel.add(searchButton);
        topPanel.add(backButton);

        resultArea = new JTextArea(10, 40);
        resultArea.setEditable(false);
        resultArea.setFont(new Font("Arial", Font.PLAIN, 12));
        JScrollPane scrollPane = new JScrollPane(resultArea); // Sửa lỗi: thêm resultArea vào scrollPane

        panel.add(topPanel, BorderLayout.NORTH);
        panel.add(scrollPane, BorderLayout.CENTER);

        add(panel);
    }

    private void search() {
        String searchText = searchField.getText().trim();
        if (searchText.isEmpty()) {
            JOptionPane.showMessageDialog(this, "Vui lòng nhập tên hàng hóa!");
            return;
        }

        try (Connection conn = DriverManager.getConnection("****************************************", "root", "")) {
            String sql = "SELECT * FROM products WHERE name LIKE ?";
            PreparedStatement stmt = conn.prepareStatement(sql);
            stmt.setString(1, "%" + searchText + "%"); // Sửa lỗi: logic tìm kiếm đúng
            ResultSet rs = stmt.executeQuery();

            StringBuilder result = new StringBuilder();
            boolean found = false;
            while (rs.next()) {
                found = true;
                result.append("ID: ").append(rs.getInt("id")).append("\n");
                result.append("Tên: ").append(rs.getString("name")).append("\n");
                result.append("Số lượng: ").append(rs.getInt("quantity")).append("\n");
                result.append("Giá: ").append(currencyFormat.format(rs.getDouble("price"))).append("\n"); // Format tiền VND
                result.append("Mô tả: ").append(rs.getString("description")).append("\n");
                result.append("─────────────────────────────────────\n\n");
            }

            // Sửa lỗi: logic hiển thị kết quả
            if (found) {
                resultArea.setText(result.toString());
            } else {
                resultArea.setText("Không tìm thấy hàng hóa nào với từ khóa: \"" + searchText + "\"");
            }
        } catch (SQLException ex) {
            JOptionPane.showMessageDialog(this, "Lỗi kết nối: " + ex.getMessage());
        }
    }
}