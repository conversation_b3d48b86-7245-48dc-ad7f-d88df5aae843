-- Tạo database cho hệ thống quản lý hàng hóa
CREATE DATABASE IF NOT EXISTS inventory_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE inventory_db;

-- Tạo bảng users để lưu thông tin người dùng (chỉ cho admin)
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL, -- Mật khẩu đã được hash bằng BCrypt
    role ENUM('admin', 'user') DEFAULT 'user',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tạo bảng categories để lưu danh mục sản phẩm
CREATE TABLE IF NOT EXISTS categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tạo bảng products để lưu thông tin sản phẩm
CREATE TABLE IF NOT EXISTS products (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    category_id INT,
    quantity INT NOT NULL DEFAULT 0,
    price DECIMAL(15,2) NOT NULL DEFAULT 0.00, -- Sử dụng DECIMAL cho tiền tệ
    min_stock INT DEFAULT 10, -- Mức tồn kho tối thiểu
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL,
    INDEX idx_name (name),
    INDEX idx_price (price),
    INDEX idx_category (category_id)
);

-- Thêm dữ liệu mẫu cho bảng categories
INSERT INTO categories (name, description) VALUES
('Nội thất', 'Đồ nội thất văn phòng và gia đình'),
('Điện tử', 'Thiết bị điện tử và công nghệ'),
('Phụ kiện máy tính', 'Phụ kiện và linh kiện máy tính'),
('Âm thanh', 'Thiết bị âm thanh và giải trí');

-- Thêm dữ liệu mẫu cho bảng products
INSERT INTO products (name, category_id, quantity, price, min_stock, description) VALUES
('Cây cỏ xanh', 1, 50, 25000.00, 20, 'Cây cỏ trang trí nội thất'),
('Bàn gỗ sồi', 1, 10, 1500000.00, 5, 'Bàn làm việc bằng gỗ sồi tự nhiên'),
('Ghế xoay văn phòng', 1, 25, 850000.00, 10, 'Ghế xoay có tựa lưng cao'),
('Máy tính để bàn', 2, 8, 12000000.00, 3, 'Máy tính cấu hình cao cho văn phòng'),
('Điện thoại thông minh', 2, 30, 8500000.00, 15, 'Smartphone Android mới nhất'),
('Tai nghe Bluetooth', 4, 100, 450000.00, 30, 'Tai nghe không dây chất lượng cao'),
('Bàn phím cơ', 3, 40, 1200000.00, 15, 'Bàn phím cơ gaming RGB'),
('Chuột không dây', 3, 60, 350000.00, 20, 'Chuột quang không dây 2.4GHz'),
('Màn hình LCD 24 inch', 2, 15, 3200000.00, 8, 'Màn hình Full HD IPS'),
('Loa Bluetooth', 4, 35, 750000.00, 15, 'Loa di động chống nước');

-- Tạo bảng inventory_transactions để lưu lịch sử nhập/xuất kho
CREATE TABLE IF NOT EXISTS inventory_transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    product_id INT NOT NULL,
    transaction_type ENUM('IN', 'OUT') NOT NULL, -- IN: Nhập kho, OUT: Xuất kho
    quantity INT NOT NULL,
    price DECIMAL(15,2), -- Giá nhập/xuất
    reason VARCHAR(255), -- Lý do nhập/xuất
    reference_number VARCHAR(100), -- Số chứng từ
    created_by VARCHAR(50), -- Người thực hiện
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    INDEX idx_product (product_id),
    INDEX idx_type (transaction_type),
    INDEX idx_date (created_at)
);

-- Thêm user admin mặc định (password: admin123)
-- Tạm thời dùng plain text cho demo, production nên dùng BCrypt
INSERT INTO users (username, password, role) VALUES
('admin', 'admin123', 'admin'),
('demo', 'demo123', 'user');

-- Tạo view để xem thống kê tổng quan
CREATE VIEW product_summary AS
SELECT 
    COUNT(*) as total_products,
    SUM(quantity) as total_quantity,
    SUM(quantity * price) as total_value,
    AVG(price) as average_price,
    MIN(price) as min_price,
    MAX(price) as max_price
FROM products;

-- Tạo stored procedure để tìm kiếm sản phẩm
DELIMITER //
CREATE PROCEDURE SearchProducts(IN search_term VARCHAR(255))
BEGIN
    SELECT * FROM products 
    WHERE name LIKE CONCAT('%', search_term, '%')
    ORDER BY name;
END //
DELIMITER ;

-- Tạo stored procedure để cập nhật số lượng sản phẩm
DELIMITER //
CREATE PROCEDURE UpdateProductQuantity(
    IN product_id INT, 
    IN new_quantity INT
)
BEGIN
    UPDATE products 
    SET quantity = new_quantity, 
        updated_at = CURRENT_TIMESTAMP 
    WHERE id = product_id;
END //
DELIMITER ;

-- Tạo trigger để log khi có thay đổi sản phẩm
CREATE TABLE IF NOT EXISTS product_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    product_id INT,
    action_type ENUM('INSERT', 'UPDATE', 'DELETE'),
    old_data JSON,
    new_data JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

DELIMITER //
CREATE TRIGGER product_update_log 
AFTER UPDATE ON products
FOR EACH ROW
BEGIN
    INSERT INTO product_logs (product_id, action_type, old_data, new_data)
    VALUES (
        NEW.id, 
        'UPDATE',
        JSON_OBJECT('name', OLD.name, 'quantity', OLD.quantity, 'price', OLD.price),
        JSON_OBJECT('name', NEW.name, 'quantity', NEW.quantity, 'price', NEW.price)
    );
END //
DELIMITER ;

-- Hiển thị thông tin database đã tạo
SELECT 'Database setup completed successfully!' as status;
SELECT * FROM product_summary;
