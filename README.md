# 📦 HỆ THỐNG QUẢN LÝ HÀNG HÓA

## 📋 Mô tả dự án
Hệ thống quản lý hàng hóa được phát triển bằng Java Swing với cơ sở dữ liệu MySQL, cung cấp giao diện thân thiện để quản lý sản phẩm, danh mục và báo cáo.

## 🎯 Tính năng chính

### 🔐 Đăng nhập & Bảo mật
- Đăng nhập với username/password
- Phân quyền admin/user
- Session management

### 📊 Dashboard
- Tổng quan hệ thống
- 6 chức năng chính:
  - Tra cứu sản phẩm
  - Quản lý sản phẩm  
  - Quản lý danh mục
  - Nhập/Xuất kho
  - Báo cáo & Thống kê
  - Đăng xuất

### 🔍 Tra cứu sản phẩm
- Tìm kiếm theo tên sản phẩm
- Hiển thị thông tin chi tiết
- Giao diện thân thiện

### 📦 Quản lý sản phẩm
- Thêm/Sửa/<PERSON><PERSON><PERSON> sản phẩm
- Quản lý số lượng tồn kho
- Phân loại theo danh mục
- Thiết lập mức tồn kho tối thiểu

### 📂 Quản lý danh mục
- Tạo/Sửa/Xóa danh mục
- Thống kê số sản phẩm theo danh mục
- Giao diện hiện đại

### 📈 Báo cáo & Thống kê
- Báo cáo tồn kho
- Thống kê theo danh mục
- Lịch sử nhập/xuất

## 🛠️ Công nghệ sử dụng

### Backend
- **Java 11+**: Ngôn ngữ lập trình chính
- **Java Swing**: Framework giao diện người dùng
- **JDBC**: Kết nối cơ sở dữ liệu
- **Maven**: Quản lý dependencies

### Database
- **MySQL 8.0**: Hệ quản trị cơ sở dữ liệu
- **MySQL Connector/J**: Driver kết nối Java-MySQL

### Tools
- **NetBeans/IntelliJ**: IDE phát triển
- **MySQL Workbench**: Quản lý database
- **Git**: Version control

## 📁 Cấu trúc dự án

```
InventoryManagement/
├── src/main/java/com/mycompany/inventorymanagement/
│   ├── LoginFrame.java          # Giao diện đăng nhập
│   ├── MainFrame.java           # Dashboard chính
│   ├── SearchFrame.java         # Tra cứu sản phẩm
│   ├── CategoryFrame.java       # Quản lý sản phẩm
│   ├── CategoryManagementFrame.java # Quản lý danh mục
│   ├── InventoryFrame.java      # Nhập/Xuất kho
│   ├── ReportFrame.java         # Báo cáo & Thống kê
│   └── UIUtils.java             # Utilities giao diện
├── database_setup.sql           # Script tạo database
├── update_database.sql          # Script cập nhật dữ liệu
├── mysql-connector-j-8.0.33.jar # MySQL driver
├── pom.xml                      # Maven configuration
└── README.md                    # Tài liệu dự án
```

## 🗄️ Cơ sở dữ liệu

### Bảng chính
1. **users**: Quản lý người dùng
2. **categories**: Danh mục sản phẩm (10 danh mục)
3. **products**: Sản phẩm (53+ sản phẩm)
4. **inventory_transactions**: Lịch sử nhập/xuất

### Dữ liệu mẫu
- **10 danh mục**: Nội thất, Điện tử, Phụ kiện máy tính, Âm thanh, Văn phòng phẩm, Thời trang, Gia dụng, Sách, Thể thao, Làm đẹp
- **53+ sản phẩm**: Đa dạng từ 5,000đ đến 18,000,000đ
- **Giao dịch mẫu**: Nhập/xuất kho với lý do và người thực hiện

## 🚀 Hướng dẫn cài đặt

### 1. Yêu cầu hệ thống
- Java JDK 11 trở lên
- MySQL Server 8.0
- IDE: NetBeans/IntelliJ/Eclipse
- RAM: 4GB+
- Disk: 500MB+

### 2. Cài đặt MySQL
```bash
# Windows: Tải MySQL Installer
# macOS: brew install mysql
# Ubuntu: sudo apt install mysql-server
```

### 3. Tạo database
```sql
mysql -u root -p < database_setup.sql
```

### 4. Cấu hình kết nối
Cập nhật thông tin kết nối trong các file Java:
```java
String url = "****************************************";
String username = "root";
String password = ""; // Thay đổi password của bạn
```

### 5. Compile và chạy
```bash
# Compile
javac -cp "mysql-connector-j-8.0.33.jar" -d target/classes src/main/java/com/mycompany/inventorymanagement/*.java

# Run
java -cp "target/classes;mysql-connector-j-8.0.33.jar" com.mycompany.inventorymanagement.LoginFrame
```

## 👤 Tài khoản demo
- **Admin**: `admin` / `admin123`
- **User**: `demo` / `demo123`

## 🎨 Giao diện

### Đặc điểm thiết kế
- **Modern UI**: Gradient background, rounded corners
- **Responsive**: Tự động điều chỉnh kích thước
- **Color Scheme**: Xanh dương chủ đạo (#3498db)
- **Typography**: Segoe UI font family
- **No Animation**: Theo yêu cầu không có hover effects

### Screenshots
1. **Login**: Giao diện đăng nhập đẹp mắt
2. **Dashboard**: 6 cards chức năng rõ ràng
3. **Search**: Tìm kiếm với kết quả chi tiết
4. **Category Management**: Quản lý danh mục hiện đại
5. **Product Management**: Bảng sản phẩm đầy đủ

## 🧪 Hướng dẫn test

### Test cơ bản
1. **Đăng nhập**: Test với admin/admin123
2. **Dashboard**: Click từng card để kiểm tra navigation
3. **Tìm kiếm**: Tìm "chuot", "ban", "dien thoai"
4. **Quản lý danh mục**: Thêm/sửa/xóa danh mục
5. **Quản lý sản phẩm**: CRUD operations

### Test nâng cao
1. **Database connection**: Test khi MySQL offline
2. **Data validation**: Nhập dữ liệu không hợp lệ
3. **Performance**: Test với nhiều dữ liệu
4. **UI responsiveness**: Resize window

## 🐛 Troubleshooting

### Lỗi thường gặp
1. **ClassNotFoundException**: Thiếu MySQL driver
   ```bash
   # Solution: Đảm bảo mysql-connector-j-8.0.33.jar trong classpath
   ```

2. **SQLException**: Không kết nối được MySQL
   ```bash
   # Solution: Kiểm tra MySQL service, username/password
   ```

3. **Font issues**: Lỗi hiển thị tiếng Việt
   ```bash
   # Solution: Đã sử dụng text ASCII thay emoji
   ```

### Performance tips
- Sử dụng connection pooling cho production
- Index các cột tìm kiếm thường xuyên
- Optimize SQL queries với EXPLAIN

## 📈 Tính năng nâng cao (Future)

### Phase 2
- [ ] Export báo cáo Excel/PDF
- [ ] Backup/Restore database
- [ ] Multi-language support
- [ ] Dark/Light theme

### Phase 3
- [ ] Web interface
- [ ] REST API
- [ ] Mobile app
- [ ] Cloud deployment

## 👥 Đóng góp
1. Fork repository
2. Tạo feature branch
3. Commit changes
4. Push to branch
5. Create Pull Request

## 📄 License
MIT License - Xem file LICENSE để biết thêm chi tiết

## 📞 Liên hệ
- **Developer**: [Tên của bạn]
- **Email**: [Email của bạn]
- **GitHub**: [GitHub profile]

---
*Dự án được phát triển cho mục đích học tập và thực hành Java Swing + MySQL*
