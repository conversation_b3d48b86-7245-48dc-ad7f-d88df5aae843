package com.mycompany.inventorymanagement;

import javax.swing.*;
import javax.swing.border.Border;
import java.awt.*;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;

/**
 * Utility class for consistent UI styling across the application
 */
public class UIUtils {
    
    // Color Palette
    public static final Color PRIMARY_COLOR = new Color(52, 152, 219);      // Blue
    public static final Color PRIMARY_DARK = new Color(41, 128, 185);       // Darker Blue
    public static final Color SECONDARY_COLOR = new Color(46, 204, 113);    // Green
    public static final Color ACCENT_COLOR = new Color(155, 89, 182);       // Purple
    public static final Color DANGER_COLOR = new Color(231, 76, 60);        // Red
    public static final Color WARNING_COLOR = new Color(243, 156, 18);      // Orange
    public static final Color LIGHT_GRAY = new Color(236, 240, 241);        // Light Gray
    public static final Color DARK_GRAY = new Color(52, 73, 94);            // Dark Gray
    public static final Color WHITE = Color.WHITE;
    public static final Color BLACK = new Color(44, 62, 80);                // Dark Blue-Gray
    
    // Fonts
    public static final Font TITLE_FONT = new Font("Segoe UI", Font.BOLD, 24);
    public static final Font SUBTITLE_FONT = new Font("Segoe UI", Font.BOLD, 18);
    public static final Font BODY_FONT = new Font("Segoe UI", Font.PLAIN, 14);
    public static final Font SMALL_FONT = new Font("Segoe UI", Font.PLAIN, 12);
    public static final Font BUTTON_FONT = new Font("Segoe UI", Font.BOLD, 14);
    
    // Dimensions
    public static final Dimension BUTTON_SIZE = new Dimension(200, 45);
    public static final Dimension SMALL_BUTTON_SIZE = new Dimension(120, 35);
    public static final Dimension LARGE_BUTTON_SIZE = new Dimension(250, 55);
    
    /**
     * Creates a styled primary button
     */
    public static JButton createPrimaryButton(String text) {
        JButton button = new JButton(text);
        styleButton(button, PRIMARY_COLOR, WHITE, PRIMARY_DARK);
        return button;
    }
    
    /**
     * Creates a styled secondary button
     */
    public static JButton createSecondaryButton(String text) {
        JButton button = new JButton(text);
        styleButton(button, SECONDARY_COLOR, WHITE, new Color(39, 174, 96));
        return button;
    }
    
    /**
     * Creates a styled danger button
     */
    public static JButton createDangerButton(String text) {
        JButton button = new JButton(text);
        styleButton(button, DANGER_COLOR, WHITE, new Color(192, 57, 43));
        return button;
    }
    
    /**
     * Creates a styled warning button
     */
    public static JButton createWarningButton(String text) {
        JButton button = new JButton(text);
        styleButton(button, WARNING_COLOR, WHITE, new Color(211, 84, 0));
        return button;
    }
    
    /**
     * Creates a styled outline button
     */
    public static JButton createOutlineButton(String text) {
        JButton button = new JButton(text);
        button.setFont(BUTTON_FONT);
        button.setPreferredSize(BUTTON_SIZE);
        button.setBackground(WHITE);
        button.setForeground(PRIMARY_COLOR);
        button.setBorder(BorderFactory.createLineBorder(PRIMARY_COLOR, 2));
        button.setFocusPainted(false);
        button.setCursor(new Cursor(Cursor.HAND_CURSOR));
        
        // Hover effect
        button.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseEntered(MouseEvent e) {
                button.setBackground(PRIMARY_COLOR);
                button.setForeground(WHITE);
            }
            
            @Override
            public void mouseExited(MouseEvent e) {
                button.setBackground(WHITE);
                button.setForeground(PRIMARY_COLOR);
            }
        });
        
        return button;
    }
    
    /**
     * Applies consistent styling to a button
     */
    private static void styleButton(JButton button, Color bgColor, Color fgColor, Color hoverColor) {
        button.setFont(BUTTON_FONT);
        button.setPreferredSize(BUTTON_SIZE);
        button.setBackground(bgColor);
        button.setForeground(fgColor);
        button.setBorder(BorderFactory.createEmptyBorder(10, 20, 10, 20));
        button.setFocusPainted(false);
        button.setCursor(new Cursor(Cursor.HAND_CURSOR));
        
        // Hover effect
        button.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseEntered(MouseEvent e) {
                button.setBackground(hoverColor);
            }
            
            @Override
            public void mouseExited(MouseEvent e) {
                button.setBackground(bgColor);
            }
        });
    }
    
    /**
     * Creates a styled text field
     */
    public static JTextField createStyledTextField() {
        JTextField textField = new JTextField();
        textField.setFont(BODY_FONT);
        textField.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(LIGHT_GRAY, 1),
            BorderFactory.createEmptyBorder(8, 12, 8, 12)
        ));
        textField.setPreferredSize(new Dimension(200, 40));
        return textField;
    }
    
    /**
     * Creates a styled password field
     */
    public static JPasswordField createStyledPasswordField() {
        JPasswordField passwordField = new JPasswordField();
        passwordField.setFont(BODY_FONT);
        passwordField.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(LIGHT_GRAY, 1),
            BorderFactory.createEmptyBorder(8, 12, 8, 12)
        ));
        passwordField.setPreferredSize(new Dimension(200, 40));
        return passwordField;
    }
    
    /**
     * Creates a styled label
     */
    public static JLabel createStyledLabel(String text) {
        JLabel label = new JLabel(text);
        label.setFont(BODY_FONT);
        label.setForeground(BLACK);
        return label;
    }
    
    /**
     * Creates a title label
     */
    public static JLabel createTitleLabel(String text) {
        JLabel label = new JLabel(text, SwingConstants.CENTER);
        label.setFont(TITLE_FONT);
        label.setForeground(BLACK);
        return label;
    }
    
    /**
     * Creates a subtitle label
     */
    public static JLabel createSubtitleLabel(String text) {
        JLabel label = new JLabel(text, SwingConstants.CENTER);
        label.setFont(SUBTITLE_FONT);
        label.setForeground(DARK_GRAY);
        return label;
    }

    /**
     * Creates a styled panel with padding
     */
    public static JPanel createStyledPanel() {
        JPanel panel = new JPanel();
        panel.setBackground(WHITE);
        panel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        return panel;
    }

    /**
     * Creates a card panel with shadow effect
     */
    public static JPanel createCardPanel() {
        JPanel panel = new JPanel();
        panel.setBackground(WHITE);
        panel.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(LIGHT_GRAY, 1),
            BorderFactory.createEmptyBorder(20, 20, 20, 20)
        ));
        return panel;
    }

    /**
     * Creates a gradient panel
     */
    public static JPanel createGradientPanel(Color color1, Color color2) {
        return new JPanel() {
            @Override
            protected void paintComponent(Graphics g) {
                super.paintComponent(g);
                Graphics2D g2d = (Graphics2D) g;
                g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
                int w = getWidth(), h = getHeight();
                GradientPaint gp = new GradientPaint(0, 0, color1, 0, h, color2);
                g2d.setPaint(gp);
                g2d.fillRect(0, 0, w, h);
            }
        };
    }

    /**
     * Creates a styled table
     */
    public static void styleTable(JTable table) {
        table.setFont(BODY_FONT);
        table.setRowHeight(35);
        table.setGridColor(LIGHT_GRAY);
        table.setSelectionBackground(PRIMARY_COLOR);
        table.setSelectionForeground(WHITE);
        table.getTableHeader().setFont(BUTTON_FONT);
        table.getTableHeader().setBackground(DARK_GRAY);
        table.getTableHeader().setForeground(WHITE);
        table.getTableHeader().setPreferredSize(new Dimension(0, 40));
    }

    /**
     * Creates a styled combo box
     */
    public static JComboBox<String> createStyledComboBox() {
        JComboBox<String> comboBox = new JComboBox<>();
        comboBox.setFont(BODY_FONT);
        comboBox.setPreferredSize(new Dimension(200, 40));
        comboBox.setBackground(WHITE);
        return comboBox;
    }

    /**
     * Creates a styled text area
     */
    public static JTextArea createStyledTextArea() {
        JTextArea textArea = new JTextArea();
        textArea.setFont(BODY_FONT);
        textArea.setBackground(WHITE);
        textArea.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(LIGHT_GRAY, 1),
            BorderFactory.createEmptyBorder(10, 10, 10, 10)
        ));
        return textArea;
    }

    /**
     * Sets the application look and feel
     */
    public static void setLookAndFeel() {
        try {
            UIManager.setLookAndFeel(UIManager.getSystemLookAndFeel());
        } catch (Exception e) {
            // Fallback to cross-platform look and feel
            try {
                UIManager.setLookAndFeel(UIManager.getCrossPlatformLookAndFeel());
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }

        // Customize UI defaults
        UIManager.put("Button.font", BUTTON_FONT);
        UIManager.put("Label.font", BODY_FONT);
        UIManager.put("TextField.font", BODY_FONT);
        UIManager.put("TextArea.font", BODY_FONT);
        UIManager.put("ComboBox.font", BODY_FONT);
        UIManager.put("Table.font", BODY_FONT);
        UIManager.put("TableHeader.font", BUTTON_FONT);
    }

    /**
     * Shows a styled success message
     */
    public static void showSuccessMessage(Component parent, String message) {
        JOptionPane.showMessageDialog(parent, message, "Thành công", JOptionPane.INFORMATION_MESSAGE);
    }

    /**
     * Shows a styled error message
     */
    public static void showErrorMessage(Component parent, String message) {
        JOptionPane.showMessageDialog(parent, message, "Lỗi", JOptionPane.ERROR_MESSAGE);
    }

    /**
     * Shows a styled warning message
     */
    public static void showWarningMessage(Component parent, String message) {
        JOptionPane.showMessageDialog(parent, message, "Cảnh báo", JOptionPane.WARNING_MESSAGE);
    }

    /**
     * Shows a styled confirmation dialog
     */
    public static int showConfirmDialog(Component parent, String message) {
        return JOptionPane.showConfirmDialog(parent, message, "Xác nhận",
            JOptionPane.YES_NO_OPTION, JOptionPane.QUESTION_MESSAGE);
    }
}
